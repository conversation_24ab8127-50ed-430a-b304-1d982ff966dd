import { Input } from "@/shared/components/ui/input";
import { motion, AnimatePresence } from "framer-motion";
import { Calculator, DollarSign, Percent, TrendingUp, AlertCircle, CheckCircle2, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { parsePrice } from "@/shared/utils/price-formatter";
import { ICreateStock } from "../../validators/create-stock.validator";

interface PricingCalculatorProps {
	index: number;
	methodsForm: UseFormReturn<ICreateStock>;
	isOpen: boolean;
	onClose: () => void;
}

export const PricingCalculator: React.FC<PricingCalculatorProps> = ({ index, methodsForm, isOpen, onClose }) => {
	const { watch, setValue } = methodsForm;

	const costPriceRaw = watch(`inventories.${index}.stockMovement.product.costPrice`);
	const priceRaw = watch(`inventories.${index}.stockMovement.product.price`);

	const costPrice = parsePrice(costPriceRaw);
	const price = parsePrice(priceRaw);

	const [marginPercentage, setMarginPercentage] = useState<number | undefined>(undefined);

	const suggestedMargins = [
		{ value: 20, label: "Básica", color: "bg-blue-500" },
		{ value: 30, label: "Padrão", color: "bg-green-500" },
		{ value: 40, label: "Premium", color: "bg-purple-500" },
	];

	const calculateProfit = (): { value: number; margin: number; roi: number } => {
		if (!costPrice || !price) return { value: 0, margin: 0, roi: 0 };

		const profit = price - costPrice;
		const margin = price > 0 ? (profit / price) * 100 : 0;
		const roi = costPrice > 0 ? (profit / costPrice) * 100 : 0;

		return {
			value: Number(profit.toFixed(2)),
			margin: Number(margin.toFixed(2)),
			roi: Number(roi.toFixed(2)),
		};
	};

	const calculatePriceFromMargin = (margin: number): number => {
		if (!costPrice || margin === undefined) return 0;
		if (margin === 100) return costPrice * 100;
		const calculatedPrice = costPrice / (1 - margin / 100);
		return Number(calculatedPrice.toFixed(2));
	};

	const formatCurrency = (value: number): string => {
		return new Intl.NumberFormat("pt-BR", {
			style: "currency",
			currency: "BRL",
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(value);
	};

	useEffect(() => {
		if (costPrice > 0 && price > 0) {
			const profit = price - costPrice;
			const newMargin = price > 0 ? (profit / price) * 100 : 0;
			setMarginPercentage(Number(newMargin.toFixed(2)));
		}
	}, [costPrice, price]);

	const handleMarginChange = (newMargin: number) => {
		console.log("handleMarginChange called:", {
			newMargin,
			costPrice,
			costPriceRaw,
			costPriceType: typeof costPrice,
			costPriceRawType: typeof costPriceRaw,
		});
		setMarginPercentage(newMargin);
		if (costPrice > 0) {
			const calculatedPrice = calculatePriceFromMargin(newMargin);
			console.log("Setting calculated price:", { calculatedPrice, costPrice, newMargin });
			setValue(`inventories.${index}.stockMovement.product.price`, calculatedPrice, {
				shouldValidate: true,
				shouldDirty: true,
			});
		} else {
			console.log("Cost price is 0 or invalid, not calculating");
		}
	};

	return (
		<AnimatePresence>
			{isOpen && (
				<motion.div
					initial={{ opacity: 0, y: -10, scale: 0.95 }}
					animate={{ opacity: 1, y: 0, scale: 1 }}
					exit={{ opacity: 0, y: -10, scale: 0.95 }}
					transition={{ duration: 0.2 }}
					className="absolute top-full left-0 right-0 z-50 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg p-4"
				>
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-2">
							<div className="p-1.5 bg-mainColor/10 rounded-lg">
								<Calculator size={16} className="text-mainColor" />
							</div>
							<span className="text-sm font-medium text-gray-700">Calculadora de Preços</span>
						</div>
						<button onClick={onClose} className="p-1 hover:bg-gray-100 rounded-full transition-colors">
							<X size={16} className="text-gray-500" />
						</button>
					</div>

					{/* Margens Sugeridas */}
					<div className="mb-4">
						<div className="flex items-center gap-2 mb-2">
							<Percent size={14} className="text-gray-500" />
							<span className="text-xs font-medium text-gray-600">Margens Sugeridas</span>
						</div>
						<div className="flex gap-2">
							{suggestedMargins.map(({ value, label, color }) => (
								<button
									key={value}
									type="button"
									onClick={() => handleMarginChange(value)}
									className={`
										px-3 py-1.5 rounded-lg text-xs font-medium
										transition-all duration-200 flex items-center gap-1.5
										${marginPercentage === value ? "bg-mainColor text-white shadow-md" : "bg-gray-100 text-gray-700 hover:bg-mainColor/10"}
									`}
								>
									<div className={`w-2 h-2 rounded-full ${color}`} />
									{label} ({value}%)
								</button>
							))}
						</div>
					</div>

					{/* Campo de Margem Personalizada */}
					<div className="mb-4">
						<div className="flex items-center gap-2 mb-2">
							<TrendingUp size={14} className="text-gray-500" />
							<span className="text-xs font-medium text-gray-600">Margem Personalizada</span>
						</div>
						<div className="relative">
							<NumericFormat
								value={marginPercentage ?? ""}
								onValueChange={values => {
									const newMargin = values.floatValue;
									if (newMargin !== undefined) {
										handleMarginChange(newMargin);
									}
								}}
								suffix="%"
								decimalScale={2}
								placeholder="Ex: 25,00%"
								thousandSeparator="."
								decimalSeparator=","
								customInput={Input}
								className={`
									w-full px-3 py-2 text-sm transition-all duration-200
									border rounded-lg bg-white
									focus:outline-none focus:ring-2 focus:ring-mainColor/50
									${
										marginPercentage !== undefined && marginPercentage > 0
											? marginPercentage < 15
												? "border-red-300 bg-red-50/50"
												: marginPercentage > 50
													? "border-yellow-300 bg-yellow-50/50"
													: "border-green-300 bg-green-50/50"
											: "border-gray-200"
									}
								`}
							/>
							{marginPercentage !== undefined && marginPercentage > 0 && (
								<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="absolute right-3 top-1/2 -translate-y-1/2">
									{marginPercentage < 15 ? (
										<AlertCircle size={14} className="text-red-500" />
									) : marginPercentage > 50 ? (
										<AlertCircle size={14} className="text-yellow-500" />
									) : (
										<CheckCircle2 size={14} className="text-green-500" />
									)}
								</motion.div>
							)}
						</div>
						{marginPercentage !== undefined && marginPercentage > 0 && (
							<motion.div
								initial={{ opacity: 0, y: -5 }}
								animate={{ opacity: 1, y: 0 }}
								className={`text-xs font-medium mt-1 ${
									marginPercentage < 15 ? "text-red-500" : marginPercentage > 50 ? "text-yellow-500" : "text-green-500"
								}`}
							>
								{marginPercentage < 15
									? "⚠️ Margem muito baixa"
									: marginPercentage > 50
										? "⚠️ Margem muito alta"
										: "✅ Margem adequada"}
							</motion.div>
						)}
					</div>

					{/* Resumo dos Cálculos */}
					{(costPrice > 0 || price > 0) && (
						<motion.div initial={{ opacity: 0, y: -5 }} animate={{ opacity: 1, y: 0 }} className="bg-gray-50 rounded-lg p-3 space-y-2">
							<div className="flex items-center gap-2 mb-2">
								<DollarSign size={14} className="text-gray-500" />
								<span className="text-xs font-medium text-gray-600">Resumo</span>
							</div>

							<div className="grid grid-cols-3 gap-2 text-center">
								<div className="bg-white rounded-lg p-2">
									<span className="text-xs text-gray-500 block">Lucro</span>
									<p className="text-sm font-semibold text-mainColor">{formatCurrency(calculateProfit().value)}</p>
								</div>

								<div className="bg-white rounded-lg p-2">
									<span className="text-xs text-gray-500 block">Margem</span>
									<p className={`text-sm font-semibold ${calculateProfit().margin >= 0 ? "text-green-600" : "text-red-600"}`}>
										{calculateProfit().margin.toFixed(1)}%
									</p>
								</div>

								<div className="bg-white rounded-lg p-2">
									<span className="text-xs text-gray-500 block">ROI</span>
									<p className="text-sm font-semibold text-blue-600">{calculateProfit().roi.toFixed(1)}%</p>
								</div>
							</div>

							{calculateProfit().margin < 0 && (
								<motion.div
									initial={{ opacity: 0, scale: 0.95 }}
									animate={{ opacity: 1, scale: 1 }}
									className="flex items-center gap-2 bg-red-50 text-red-600 p-2 rounded-lg border border-red-100 mt-2"
								>
									<AlertCircle size={14} />
									<span className="text-xs font-medium">Preço de venda menor que o custo!</span>
								</motion.div>
							)}
						</motion.div>
					)}
				</motion.div>
			)}
		</AnimatePresence>
	);
};
